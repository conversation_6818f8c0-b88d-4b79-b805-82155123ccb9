package com.kingsoft.ai.hanhai.basematrix.system.api.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 空间
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
public class RemoteSpaceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 空间ID
     */
    private Long spaceId;

    /**
     * 空间名称
     */
    private String spaceName;

}
