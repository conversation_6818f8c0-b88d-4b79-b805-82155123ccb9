package com.kingsoft.ai.hanhai.basematrix.system.api;

import com.kingsoft.ai.hanhai.basematrix.system.api.domain.vo.RemoteSpaceVo;
import org.apache.dubbo.remoting.http12.HttpMethods;
import org.apache.dubbo.remoting.http12.rest.Mapping;

import java.util.List;
import java.util.Set;

/**
 * 空间服务
 *
 * <AUTHOR>
 */
public interface RemoteSpaceService {

    /**
     * 查询空间列表
     *
     * @param tenantId 租户id
     * @return 结果
     */
    List<RemoteSpaceVo> selectByTenantId(String tenantId);

    /**
     * 查询用户空间列表
     * @param userId
     * @return
     */
    Set<Long> selectSpaceIdsByUserId(Long userId);

    /**
     * 获取空间信息
     *
     * @param spaceId 空间id
     * @return 结果
     */
    @Mapping(path = "/getSpaceInfoById", method = HttpMethods.GET)
    RemoteSpaceVo getSpaceInfoById(Long spaceId);
}
