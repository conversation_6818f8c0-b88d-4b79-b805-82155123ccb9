package com.kingsoft.ai.hanhai.basematrix.system.api.domain.bo;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.io.Serial;
import java.io.Serializable;
import java.net.URI;
import java.util.*;

/**
 * 网关路由
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RemoteGatewayRouteBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @NotEmpty
    private String id;

    @NotEmpty
    @Valid
    private List<PredicateDefinition> predicates = new ArrayList<>();

    @Valid
    private List<FilterDefinition> filters = new ArrayList<>();

    @NotNull
    private URI uri;

    private Map<String, Object> metadata = new HashMap<>();

    private int order = 0;

    private boolean enabled = true;

    public static class PredicateDefinitionSerializer extends JsonSerializer<PredicateDefinition> {
        @Override
        public void serialize(RemoteGatewayRouteBo.PredicateDefinition value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value.getRawName() != null && !value.getRawName().isEmpty()) {
                gen.writeString(value.getRawName());
            } else {
                gen.writeStartObject();
                gen.writeStringField("name", value.getName());
                gen.writeObjectField("args", value.getArgs());
                gen.writeEndObject();
            }
        }
    }

    public static class FilterDefinitionSerializer extends JsonSerializer<RemoteGatewayRouteBo.FilterDefinition> {
        @Override
        public void serialize(RemoteGatewayRouteBo.FilterDefinition value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value.getRawName() != null && !value.getRawName().isEmpty()) {
                gen.writeString(value.getRawName());
            } else {
                gen.writeStartObject();
                gen.writeStringField("name", value.getName());
                gen.writeObjectField("args", value.getArgs());
                gen.writeEndObject();
            }
        }
    }

    @Data
    @NoArgsConstructor
    @JsonSerialize(using = PredicateDefinitionSerializer.class)
    public static class PredicateDefinition {
        @NotNull
        private String name;
        private Map<String, String> args = new LinkedHashMap<>();
        @JsonIgnore
        private String rawName;

        @JsonCreator
        public PredicateDefinition(String text) {
            rawName = text;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Map<String, String> getArgs() {
            return args;
        }

        public void setArgs(Map<String, String> args) {
            this.args = args;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            PredicateDefinition that = (PredicateDefinition) o;
            return Objects.equals(name, that.name) && Objects.equals(args, that.args);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, args);
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("PredicateDefinition{");
            sb.append("name='").append(name).append('\'');
            sb.append(", args=").append(args);
            sb.append('}');
            return sb.toString();
        }
    }

    @Data
    @NoArgsConstructor
    @JsonSerialize(using = FilterDefinitionSerializer.class)
    public static class FilterDefinition {
        @NotNull
        private String name;
        private Map<String, String> args = new LinkedHashMap<>();
        @JsonIgnore
        private String rawName;

        @JsonCreator
        public FilterDefinition(String text) {
            rawName = text;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Map<String, String> getArgs() {
            return args;
        }

        public void setArgs(Map<String, String> args) {
            this.args = args;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            FilterDefinition that = (FilterDefinition) o;
            return Objects.equals(name, that.name) && Objects.equals(args, that.args);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, args);
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("FilterDefinition{");
            sb.append("name='").append(name).append('\'');
            sb.append(", args=").append(args);
            sb.append('}');
            return sb.toString();
        }
    }
}
