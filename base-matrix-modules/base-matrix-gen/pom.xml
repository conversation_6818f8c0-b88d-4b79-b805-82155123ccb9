<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.kingsoft</groupId>
        <artifactId>base-matrix-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>base-matrix-gen</artifactId>

    <description>
        base-matrix-gen代码生成
    </description>

    <dependencies>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-nacos</artifactId>
        </dependency>

        <!-- Apache Velocity -->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>

        <!-- base-matrix Common Log -->
        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kingsoft</groupId>
            <artifactId>base-matrix-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-environment-spring-data-jdbc</artifactId>
            <version>${anyline.version}</version>
        </dependency>

        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-mysql</artifactId>
            <version>${anyline.version}</version>
        </dependency>

        <!-- anyline支持100+种类型数据库 添加对应的jdbc依赖与anyline对应数据库依赖包即可 -->
<!--        <dependency>-->
<!--            <groupId>org.anyline</groupId>-->
<!--            <artifactId>anyline-data-jdbc-oracle</artifactId>-->
<!--            <version>${anyline.version}</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.anyline</groupId>-->
<!--            <artifactId>anyline-data-jdbc-postgresql</artifactId>-->
<!--            <version>${anyline.version}</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.anyline</groupId>-->
<!--            <artifactId>anyline-data-jdbc-mssql</artifactId>-->
<!--            <version>${anyline.version}</version>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
